<!-- Enhanced Release Success Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-10 mx-auto p-6 border max-w-lg shadow-lg rounded-xl bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <div class="text-center">
            <!-- Success Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
            </div>
            
            <!-- Success Message -->
            <h3 class="text-xl leading-6 font-semibold text-gray-900 mb-4">
                Release Successful!
            </h3>
            <div class="mb-6">
                <p class="text-sm text-gray-600 mb-4">
                    {{ success_message }}
                </p>
                
                {% if release_details %}
                    <!-- Release Details -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-left">
                        <h4 class="text-sm font-medium text-green-900 mb-3">Release Details</h4>
                        
                        {% if release_details.released_items %}
                            <div class="space-y-2">
                                <div class="text-xs text-green-700 font-medium">Released Items:</div>
                                {% for item in release_details.released_items %}
                                    <div class="flex justify-between text-xs text-green-800">
                                        <span>{{ item.item }}</span>
                                        <span>{{ item.quantity }} {{ item.unit }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {% if release_details.failed_items %}
                            <div class="mt-4 pt-3 border-t border-green-300">
                                <div class="text-xs text-red-700 font-medium mb-2">Items Not Released:</div>
                                {% for item in release_details.failed_items %}
                                    <div class="flex justify-between text-xs text-red-800">
                                        <span>{{ item.item }}</span>
                                        <span>{{ item.available }}/{{ item.requested }} {{ item.unit }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        {% if release_details.partial_release %}
                            <div class="mt-3 pt-3 border-t border-green-300">
                                <div class="flex items-center text-xs text-yellow-700">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    This was a partial release
                                </div>
                            </div>
                        {% endif %}
                        
                        <div class="mt-3 pt-3 border-t border-green-300 text-xs text-green-700">
                            <div>Released by: {{ release_details.released_by }}</div>
                            <div>Released at: {{ release_details.released_at|date:"M d, Y g:i A" }}</div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
            <button type="button"
                    @click="show = false; setTimeout(() => { htmx.ajax('GET', '{% url 'supply:release_management' %}', {target: '#release-list', swap: 'outerHTML'}); document.getElementById('modal-container').innerHTML = ''; }, 300)"
                    class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Continue & Refresh
            </button>
            {% if supply_request %}
                <a href="{% if supply_request.is_batch_request %}{% url 'supply:batch_request_detail' supply_request.id %}{% else %}{% url 'supply:gso_request_detail' supply_request.id %}{% endif %}"
                   class="bg-gray-100 border border-gray-300 rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View Request
                </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Auto-refresh release list and close modal after 5 seconds -->
<script>
    setTimeout(() => {
        // Refresh the release list
        htmx.ajax('GET', '{% url 'supply:release_management' %}', {
            target: '#release-list',
            swap: 'outerHTML'
        });
        // Clear the modal
        document.getElementById('modal-container').innerHTML = '';
    }, 5000);
</script>
