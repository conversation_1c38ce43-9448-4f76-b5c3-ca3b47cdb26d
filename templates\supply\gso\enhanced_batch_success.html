<!-- Enhanced Batch Success Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-10 mx-auto p-6 border max-w-2xl shadow-lg rounded-xl bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <div class="text-center">
            <!-- Success Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
            </div>
            
            <!-- Success Message -->
            <h3 class="text-xl leading-6 font-semibold text-gray-900 mb-4">
                {% if action == 'bulk_release' %}
                    Batch Release Completed!
                {% elif action == 'bulk_approve' %}
                    Batch Approval Completed!
                {% elif action == 'bulk_reject' %}
                    Batch Rejection Completed!
                {% else %}
                    Batch Operation Completed!
                {% endif %}
            </h3>
            
            <div class="mb-6">
                <p class="text-sm text-gray-600 mb-4">
                    {% if action == 'bulk_release' %}
                        Successfully processed {{ processed_count }} release request(s).
                        {% if failed_count > 0 %}
                            {{ failed_count }} request(s) could not be released.
                        {% endif %}
                        {% if partial_count > 0 %}
                            {{ partial_count }} request(s) were partially released.
                        {% endif %}
                    {% elif action == 'bulk_approve' %}
                        Successfully approved {{ processed_count }} request(s).
                        {% if failed_count > 0 %}
                            {{ failed_count }} request(s) could not be approved.
                        {% endif %}
                    {% elif action == 'bulk_reject' %}
                        Successfully rejected {{ processed_count }} request(s).
                    {% endif %}
                </p>
                
                {% if action == 'bulk_release' and release_details %}
                    <!-- Release Details -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-left max-h-64 overflow-y-auto">
                        <h4 class="text-sm font-medium text-green-900 mb-3">Release Summary</h4>
                        
                        <div class="space-y-3">
                            {% for detail in release_details %}
                                <div class="border-b border-green-200 pb-2 last:border-b-0">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs font-medium {% if detail.success %}text-green-800{% else %}text-red-800{% endif %}">
                                            {{ detail.request_id }}
                                        </span>
                                        <span class="text-xs {% if detail.success %}text-green-600{% else %}text-red-600{% endif %}">
                                            {% if detail.success %}✓ Success{% else %}✗ Failed{% endif %}
                                        </span>
                                    </div>
                                    
                                    {% if detail.success and detail.details.released_items %}
                                        <div class="text-xs text-green-700 space-y-1">
                                            {% for item in detail.details.released_items %}
                                                <div class="flex justify-between">
                                                    <span>{{ item.item }}</span>
                                                    <span>{{ item.quantity }} {{ item.unit }}</span>
                                                </div>
                                            {% endfor %}
                                            {% if detail.details.partial_release %}
                                                <div class="text-yellow-600 font-medium">Partial Release</div>
                                            {% endif %}
                                        </div>
                                    {% elif not detail.success %}
                                        <div class="text-xs text-red-700">
                                            {{ detail.error }}
                                        </div>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
                
                <!-- Summary Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                        <div class="text-lg font-bold text-blue-600">{{ processed_count }}</div>
                        <div class="text-xs text-blue-700">
                            {% if action == 'bulk_release' %}Released{% elif action == 'bulk_approve' %}Approved{% elif action == 'bulk_reject' %}Rejected{% else %}Processed{% endif %}
                        </div>
                    </div>
                    {% if failed_count > 0 %}
                        <div class="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
                            <div class="text-lg font-bold text-red-600">{{ failed_count }}</div>
                            <div class="text-xs text-red-700">Failed</div>
                        </div>
                    {% endif %}
                    {% if partial_count > 0 %}
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
                            <div class="text-lg font-bold text-yellow-600">{{ partial_count }}</div>
                            <div class="text-xs text-yellow-700">Partial</div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
            <button type="button"
                    @click="show = false; setTimeout(() => { htmx.ajax('GET', '{% url 'supply:release_management' %}', {target: '#release-list', swap: 'outerHTML'}); document.getElementById('modal-container').innerHTML = ''; }, 300)"
                    class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Continue & Refresh
            </button>
            <button type="button"
                    @click="show = false; document.getElementById('modal-container').innerHTML = '';"
                    class="bg-gray-100 border border-gray-300 rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Close
            </button>
        </div>
    </div>
</div>

<!-- Auto-refresh release list and close modal after 5 seconds -->
<script>
    setTimeout(() => {
        // Refresh the release list
        htmx.ajax('GET', '{% url 'supply:release_management' %}', {
            target: '#release-list',
            swap: 'outerHTML'
        });
        // Clear the modal
        document.getElementById('modal-container').innerHTML = '';
    }, 5000);
</script>
