{% extends 'base_new.html' %}

{% block title %}Release Management - MSRRMS{% endblock %}

{% block page_title %}Release Management{% endblock %}
{% block mobile_title %}Release Management{% endblock %}

{% block content %}
<div class="px-4 sm:px-0" x-data="releaseManagement()" x-init="window.releaseManagementInstance = $data">
    <div class="max-w-7xl mx-auto">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-6 md:p-8 text-white mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-6 lg:mb-0">
                    <h1 class="text-3xl md:text-4xl font-bold">Release Management</h1>
                    <p class="text-blue-100 mt-2 text-base md:text-lg">Manage approved requests ready for release</p>
                </div>
                <div class="grid grid-cols-2 lg:flex lg:items-center lg:space-x-8 gap-4 lg:gap-0">
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold">{{ total_approved }}</div>
                        <div class="text-blue-200 text-xs md:text-sm">Total Approved</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-green-200">{{ available_count }}</div>
                        <div class="text-blue-200 text-xs md:text-sm">Stock Available</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-yellow-200">{{ partial_count }}</div>
                        <div class="text-blue-200 text-xs md:text-sm">Partial Available</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl md:text-3xl font-bold text-red-200">{{ insufficient_count }}</div>
                        <div class="text-blue-200 text-xs md:text-sm">Insufficient Stock</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats Cards (Mobile) -->
        <div class="grid grid-cols-2 lg:hidden gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">{{ total_approved }}</div>
                <div class="text-sm text-gray-600">Total Approved</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                <div class="text-2xl font-bold text-green-600">{{ available_count }}</div>
                <div class="text-sm text-gray-600">Stock Available</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                <div class="text-2xl font-bold text-yellow-600">{{ partial_count }}</div>
                <div class="text-sm text-gray-600">Partial Available</div>
            </div>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
                <div class="text-2xl font-bold text-red-600">{{ insufficient_count }}</div>
                <div class="text-sm text-gray-600">Insufficient Stock</div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-5">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Filter & Search</h3>
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                    </svg>
                </div>

                <form hx-get="{% url 'supply:release_management' %}"
                      hx-target="#release-list"
                      hx-swap="outerHTML"
                      hx-indicator="#filter-loading"
                      class="space-y-4">

                    <!-- First Row: Basic Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Department Filter -->
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Department
                            </label>
                            <select name="department" id="department"
                                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                                <option value="">All Departments</option>
                                {% for dept in departments %}
                                <option value="{{ dept }}" {% if dept == department_filter %}selected{% endif %}>{{ dept }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Search -->
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Search
                            </label>
                            <input type="text" name="search" id="search" value="{{ search_query }}"
                                   placeholder="Request ID, item, or requester..."
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                        </div>

                        <!-- Stock Filter -->
                        <div>
                            <label for="stock_filter" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                Stock Status
                            </label>
                            <select name="stock_filter" id="stock_filter"
                                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                                <option value="">All Requests</option>
                                <option value="available" {% if stock_filter == 'available' %}selected{% endif %}>Stock Available</option>
                                <option value="partial" {% if stock_filter == 'partial' %}selected{% endif %}>Partial Available</option>
                                <option value="insufficient" {% if stock_filter == 'insufficient' %}selected{% endif %}>Insufficient Stock</option>
                            </select>
                        </div>

                        <!-- Request Type Filter -->
                        <div>
                            <label for="request_type" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                Request Type
                            </label>
                            <select name="request_type" id="request_type"
                                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                                <option value="">All Types</option>
                                <option value="single" {% if request_type_filter == 'single' %}selected{% endif %}>Single Item</option>
                                <option value="batch" {% if request_type_filter == 'batch' %}selected{% endif %}>Batch Request</option>
                            </select>
                        </div>
                    </div>

                    <!-- Second Row: Advanced Filters -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Date From -->
                        <div>
                            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                From Date
                            </label>
                            <input type="date" name="date_from" id="date_from" value="{{ date_from }}"
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                        </div>

                        <!-- Date To -->
                        <div>
                            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                To Date
                            </label>
                            <input type="date" name="date_to" id="date_to" value="{{ date_to }}"
                                   class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                        </div>

                        <!-- Sort -->
                        <div>
                            <label for="sort" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"></path>
                                </svg>
                                Sort By
                            </label>
                            <select name="sort" id="sort"
                                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                                <option value="-approved_at" {% if sort_by == '-approved_at' %}selected{% endif %}>Newest Approved</option>
                                <option value="approved_at" {% if sort_by == 'approved_at' %}selected{% endif %}>Oldest Approved</option>
                                <option value="department" {% if sort_by == 'department' %}selected{% endif %}>Department A-Z</option>
                                <option value="-department" {% if sort_by == '-department' %}selected{% endif %}>Department Z-A</option>
                                <option value="item__name" {% if sort_by == 'item__name' %}selected{% endif %}>Item A-Z</option>
                                <option value="-item__name" {% if sort_by == '-item__name' %}selected{% endif %}>Item Z-A</option>
                                <option value="requester__last_name" {% if sort_by == 'requester__last_name' %}selected{% endif %}>Requester A-Z</option>
                                <option value="-requester__last_name" {% if sort_by == '-requester__last_name' %}selected{% endif %}>Requester Z-A</option>
                            </select>
                        </div>

                        <!-- Page Size -->
                        <div>
                            <label for="page_size" class="block text-sm font-medium text-gray-700 mb-2">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                </svg>
                                Items per Page
                            </label>
                            <select name="page_size" id="page_size"
                                    class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors">
                                <option value="6" {% if page_size == 6 %}selected{% endif %}>6</option>
                                <option value="12" {% if page_size == 12 %}selected{% endif %}>12</option>
                                <option value="18" {% if page_size == 18 %}selected{% endif %}>18</option>
                                <option value="24" {% if page_size == 24 %}selected{% endif %}>24</option>
                            </select>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button type="submit"
                                class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg shadow-sm py-2.5 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
                            </svg>
                            <span id="filter-loading" class="htmx-indicator">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </span>
                            Apply Filters
                        </button>
                        <button type="button"
                                onclick="window.location.href='{% url 'supply:release_management' %}'"
                                class="bg-gray-100 border border-gray-300 rounded-lg shadow-sm py-2.5 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Clear Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Batch Operations -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-5">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Batch Operations</h3>
                            <p class="text-sm text-gray-500">Select multiple requests to release them together</p>
                            <div x-show="selectedRequests.length > 0" class="text-xs text-blue-600 mt-1">
                                <span x-text="selectedRequests.length"></span> request(s) selected
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <button @click="showBulkReleaseModal = true"
                                :disabled="selectedRequests.length === 0"
                                :class="selectedRequests.length === 0 ? 'bg-gray-300 cursor-not-allowed text-gray-500' : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white'"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            Release Selected (<span x-text="selectedRequests.length"></span>)
                        </button>
                        <button @click="selectedRequests = []"
                                :disabled="selectedRequests.length === 0"
                                :class="selectedRequests.length === 0 ? 'bg-gray-100 cursor-not-allowed text-gray-400' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Status Bar -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
            <div class="px-6 py-4">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full" :class="autoRefresh ? 'bg-green-500 animate-pulse' : 'bg-gray-400'"></div>
                            <span class="text-sm font-medium text-gray-700">
                                <span x-text="autoRefresh ? 'Auto-refresh ON' : 'Auto-refresh OFF'"></span>
                            </span>
                        </div>
                        <div class="text-sm text-gray-500">
                            Last updated: <span id="last-refresh-time" x-text="getTimeAgo()"></span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button @click="toggleAutoRefresh()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            <span x-text="autoRefresh ? 'Disable Auto-refresh' : 'Enable Auto-refresh'"></span>
                        </button>
                        <button @click="manualRefresh()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh Now
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests List -->
        <div id="release-list">
            {% include 'supply/gso/release_list.html' %}
        </div>

        <!-- Enhanced Bulk Release Modal -->
        <div x-show="showBulkReleaseModal"
             x-cloak
             class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
             @click.away="showBulkReleaseModal = false">
            <div class="relative top-10 mx-auto p-6 border max-w-lg shadow-lg rounded-xl bg-white">
                <form hx-post="{% url 'supply:batch_operations' %}"
                      hx-target="#release-list"
                      hx-swap="outerHTML"
                      hx-indicator="#bulk-release-loading">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="bulk_release">
                    <template x-for="requestId in selectedRequests">
                        <input type="hidden" name="selected_requests" :value="requestId">
                    </template>

                    <div class="space-y-6">
                        <!-- Header -->
                        <div class="text-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900">Bulk Release Supplies</h3>
                            <p class="text-sm text-gray-500 mt-2">
                                Release supplies for <span x-text="selectedRequests.length"></span> selected request(s)
                            </p>
                        </div>

                        <!-- Release Options -->
                        <div class="space-y-4">
                            <div>
                                <label for="release_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                    Release Notes (Optional)
                                </label>
                                <textarea name="release_notes"
                                          id="release_notes"
                                          rows="3"
                                          class="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                          placeholder="Add notes for this bulk release..."></textarea>
                            </div>

                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox"
                                               name="allow_partial"
                                               id="allow_partial"
                                               value="true"
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3">
                                        <label for="allow_partial" class="text-sm font-medium text-blue-900">
                                            Allow Partial Releases
                                        </label>
                                        <p class="text-xs text-blue-700 mt-1">
                                            Release items that have sufficient stock, even if some items in batch requests cannot be released
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-3">
                            <button type="submit"
                                    @click="showBulkReleaseModal = false; selectedRequests = []"
                                    class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 border border-transparent rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                <span id="bulk-release-loading" class="htmx-indicator">
                                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                Release Selected Requests
                            </button>
                            <button type="button"
                                    @click="showBulkReleaseModal = false"
                                    class="bg-gray-100 border border-gray-300 rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modal Container for Success/Error Messages -->
        <div id="modal-container"></div>
    </div>
</div>

<script>
function releaseManagement() {
    return {
        selectedRequests: [],
        showBulkReleaseModal: false,
        autoRefresh: true,
        refreshInterval: null,
        lastRefresh: new Date(),

        init() {
            // Start auto-refresh if enabled
            if (this.autoRefresh) {
                this.startAutoRefresh();
            }

            // Listen for HTMX events
            document.addEventListener('htmx:afterRequest', (event) => {
                if (event.detail.xhr.status === 200) {
                    this.lastRefresh = new Date();
                    this.showNotification('success', 'Updated', 'Data refreshed successfully');
                }
            });

            // Listen for visibility change to pause/resume auto-refresh
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.stopAutoRefresh();
                } else if (this.autoRefresh) {
                    this.startAutoRefresh();
                }
            });
        },

        toggleRequest(requestId) {
            const index = this.selectedRequests.indexOf(requestId);
            if (index > -1) {
                this.selectedRequests.splice(index, 1);
            } else {
                this.selectedRequests.push(requestId);
            }

            // Show feedback
            const checkbox = document.querySelector(`input[value="${requestId}"]`);
            if (checkbox) {
                checkbox.closest('.bg-white').classList.toggle('ring-2', this.selectedRequests.includes(requestId));
                checkbox.closest('.bg-white').classList.toggle('ring-blue-500', this.selectedRequests.includes(requestId));
            }
        },

        toggleAll() {
            const checkboxes = document.querySelectorAll('input[name="request_checkbox"]');
            if (this.selectedRequests.length === checkboxes.length) {
                this.selectedRequests = [];
            } else {
                this.selectedRequests = Array.from(checkboxes).map(cb => parseInt(cb.value));
            }

            // Update visual feedback for all cards
            checkboxes.forEach(checkbox => {
                const requestId = parseInt(checkbox.value);
                checkbox.closest('.bg-white').classList.toggle('ring-2', this.selectedRequests.includes(requestId));
                checkbox.closest('.bg-white').classList.toggle('ring-blue-500', this.selectedRequests.includes(requestId));
            });
        },

        startAutoRefresh() {
            this.refreshInterval = setInterval(() => {
                if (!document.hidden) {
                    htmx.ajax('GET', '{% url "supply:release_management" %}', {
                        target: '#release-list',
                        swap: 'outerHTML'
                    });
                }
            }, 30000); // Refresh every 30 seconds
        },

        stopAutoRefresh() {
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        },

        toggleAutoRefresh() {
            this.autoRefresh = !this.autoRefresh;
            if (this.autoRefresh) {
                this.startAutoRefresh();
                this.showNotification('info', 'Auto-refresh enabled', 'Page will refresh every 30 seconds');
            } else {
                this.stopAutoRefresh();
                this.showNotification('info', 'Auto-refresh disabled', 'Manual refresh required');
            }
        },

        manualRefresh() {
            htmx.ajax('GET', '{% url "supply:release_management" %}', {
                target: '#release-list',
                swap: 'outerHTML'
            });
            this.showNotification('info', 'Refreshing...', 'Updating release list');
        },

        showNotification(type, title, message) {
            if (typeof showNotification === 'function') {
                showNotification(type, title, message);
            }
        },

        getTimeAgo() {
            const now = new Date();
            const diff = Math.floor((now - this.lastRefresh) / 1000);

            if (diff < 60) return `${diff}s ago`;
            if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
            return `${Math.floor(diff / 3600)}h ago`;
        },

        // Cleanup on destroy
        destroy() {
            this.stopAutoRefresh();
        }
    }
}

// Update time display every second
setInterval(() => {
    const timeDisplay = document.getElementById('last-refresh-time');
    if (timeDisplay && window.releaseManagementInstance) {
        timeDisplay.textContent = window.releaseManagementInstance.getTimeAgo();
    }
}, 1000);
</script>
{% endblock %}
