# User Guide - Enhanced Release Management System

## Getting Started

### Accessing the System

1. **Login**: Use your GSO (General Services Office) credentials to log into the MSRRMS system
2. **Navigation**: From the main dashboard, navigate to **Supply Management** → **Release Management**
3. **Dashboard**: You'll see the enhanced release management dashboard with all approved requests ready for release

### Dashboard Overview

The release management dashboard provides:
- **Statistics Header**: Shows total approved requests, available stock, partial availability, and insufficient stock counts
- **Real-time Status Bar**: Displays auto-refresh status and last update time
- **Advanced Filters**: Comprehensive filtering options for finding specific requests
- **Batch Operations Panel**: Tools for selecting and releasing multiple requests
- **Request Cards**: Detailed cards showing each request's information and status

## Using Advanced Filters

### Basic Filtering

#### Department Filter
- **Purpose**: Filter requests by requesting department
- **Usage**: Select a department from the dropdown menu
- **Tip**: Use "All Departments" to see requests from all departments

#### Search Function
- **Purpose**: Search across multiple fields simultaneously
- **Searchable Fields**: Request ID, item name, requester name, purpose
- **Usage**: Type keywords in the search box
- **Example**: Search "laptop" to find all requests containing "laptop"

#### Stock Status Filter
- **Available**: Shows only requests where all items have sufficient stock
- **Partial Available**: Shows batch requests where some (but not all) items have sufficient stock
- **Insufficient**: Shows requests where stock is not sufficient for release

#### Request Type Filter
- **Single Item**: Shows only individual item requests
- **Batch Request**: Shows only requests containing multiple items

### Advanced Filtering

#### Date Range Filtering
- **From Date**: Set the earliest approval date to include
- **To Date**: Set the latest approval date to include
- **Format**: Use YYYY-MM-DD format or the date picker
- **Tip**: Leave blank to include all dates

#### Sorting Options
- **Newest Approved**: Most recently approved requests first
- **Oldest Approved**: Oldest approved requests first
- **Department A-Z**: Alphabetical by department name
- **Item A-Z**: Alphabetical by item name (single requests only)
- **Requester A-Z**: Alphabetical by requester's last name

#### Page Size
- **Options**: 6, 12, 18, or 24 requests per page
- **Default**: 12 requests per page
- **Tip**: Use smaller page sizes on mobile devices for better performance

### Applying Filters

1. **Set Filters**: Choose your desired filter criteria
2. **Apply**: Click "Apply Filters" button
3. **Clear**: Use "Clear Filters" to reset all filters
4. **Real-time**: Some filters update automatically as you type

## Understanding Request Cards

### Status Indicators

#### Color-Coded Status
- **Green Badge**: "Ready to Release" - All items have sufficient stock
- **Yellow Badge**: "Partial Available" - Some items available (batch requests only)
- **Red Badge**: "Insufficient Stock" - Not enough stock to fulfill request

### Request Information

#### Basic Details
- **Request ID**: Unique identifier for the request
- **Department**: Requesting department
- **Requester**: Name of the person who made the request
- **Approval Date**: When the request was approved

#### Stock Information

##### Single Item Requests
- **Requested**: Quantity requested with unit
- **Available**: Current stock available with unit
- **Shortage**: If insufficient, shows how much is short

##### Batch Requests
- **Batch Items**: Total number of items in the request
- **Stock Status**: Summary of availability across all items
- **Item Breakdown**: Available/total items ratio

### Action Buttons

#### View Details
- **Purpose**: Opens detailed view of the request
- **Information**: Shows complete request information, approval history, and item details

#### Stock Details (Batch Requests)
- **Purpose**: Shows item-by-item stock breakdown
- **Expandable**: Click to expand/collapse detailed stock information
- **Color Coding**: Green dots for available items, red dots for insufficient stock

#### Release Buttons
- **Release Now**: Available for requests with sufficient stock
- **Partial Release**: Available for batch requests with some items available
- **Cannot Release**: Disabled button for requests with insufficient stock

## Releasing Single Requests

### Standard Release Process

1. **Locate Request**: Use filters or search to find the specific request
2. **Verify Status**: Ensure the request shows "Ready to Release" (green badge)
3. **Check Details**: Review the request information and stock availability
4. **Release**: Click the "Release Now" button
5. **Confirmation**: Review the confirmation dialog and click "Confirm"
6. **Success**: View the success modal with release details

### Release Confirmation

The success modal shows:
- **Release Summary**: Confirmation message with timestamp
- **Item Details**: What was released and quantities
- **Released By**: Your name as the releasing officer
- **Next Steps**: Options to continue or view the request details

### Handling Insufficient Stock

If a request cannot be released due to insufficient stock:
1. **Status Check**: The card will show "Insufficient Stock" (red badge)
2. **Details**: Click "View Details" to see specific shortage amounts
3. **Options**:
   - Wait for restocking
   - Contact the requester to modify the request
   - Check if partial release is possible (batch requests only)

## Batch Operations

### Selecting Multiple Requests

#### Individual Selection
1. **Checkboxes**: Click the checkbox on each request card you want to select
2. **Visual Feedback**: Selected cards will have a blue border
3. **Counter**: The batch operations panel shows the number of selected requests

#### Select All
1. **Master Checkbox**: Use the "Select All" checkbox in the header
2. **Current Page**: Selects all requests on the current page
3. **Clear Selection**: Click "Clear Selection" to deselect all requests

### Batch Release Process

1. **Select Requests**: Choose the requests you want to release
2. **Batch Panel**: Review the selection count in the batch operations panel
3. **Release Button**: Click "Release Selected" button
4. **Options Modal**: Configure release options:
   - **Release Notes**: Add optional notes for the batch release
   - **Allow Partial**: Enable partial releases for batch requests with mixed availability
5. **Execute**: Click "Release Selected Requests" to proceed
6. **Results**: Review the detailed results modal

### Batch Release Results

The results modal provides:
- **Summary Statistics**: Number of successful, failed, and partial releases
- **Detailed Breakdown**: Request-by-request results
- **Item Details**: For each request, shows which items were released or failed
- **Error Information**: Specific reasons for any failures

## Partial Release Feature

### When to Use Partial Release

Partial release is useful when:
- **Batch Requests**: Some items in a batch request have sufficient stock
- **Mixed Availability**: Not all requested items are available
- **Urgent Needs**: Some items are needed immediately while waiting for others

### Partial Release Process

1. **Identify Candidates**: Look for requests with "Partial Available" status (yellow badge)
2. **Review Details**: Click "Stock Details" to see which items are available
3. **Partial Release**: Click the "Partial Release" button
4. **Confirmation**: Review which items will be released vs. which will fail
5. **Execute**: Confirm the partial release
6. **Results**: Review the partial release results

### Understanding Partial Release Results

- **Released Items**: Items that were successfully released with quantities
- **Failed Items**: Items that couldn't be released due to insufficient stock
- **Partial Flag**: Indication that this was a partial release
- **Follow-up**: Failed items remain in the system for future release when stock is available

## Real-time Features

### Auto-refresh

#### Enabling Auto-refresh
1. **Status Indicator**: Green pulsing dot indicates auto-refresh is active
2. **Toggle**: Click "Enable/Disable Auto-refresh" to control the feature
3. **Frequency**: Page refreshes every 30 seconds when enabled
4. **Smart Refresh**: Pauses when browser tab is not active

#### Manual Refresh
- **Refresh Button**: Click "Refresh Now" for immediate updates
- **Last Updated**: Shows timestamp of last refresh
- **Loading Indicator**: Shows spinning icon during refresh

### Live Updates

- **HTMX Integration**: Many actions update the page without full reload
- **Filter Updates**: Search and filter results appear instantly
- **Status Changes**: Request status updates appear in real-time
- **Notifications**: Success and error messages appear without page refresh

## Troubleshooting

### Common Issues

#### Requests Not Appearing
- **Check Filters**: Verify filter settings aren't too restrictive
- **Refresh Page**: Use manual refresh to get latest data
- **Status Check**: Ensure requests are in "APPROVED" status

#### Release Failures
- **Stock Check**: Verify current stock levels
- **Permissions**: Ensure you have GSO role permissions
- **Network Issues**: Check internet connection for HTMX requests

#### Performance Issues
- **Page Size**: Reduce items per page for better performance
- **Clear Filters**: Remove complex filters that may slow queries
- **Browser Cache**: Clear browser cache if pages load slowly

### Error Messages

#### "Invalid Request ID"
- **Cause**: Request ID is malformed or doesn't exist
- **Solution**: Return to the request list and try again

#### "Insufficient Stock"
- **Cause**: Not enough inventory to fulfill the request
- **Solution**: Wait for restocking or use partial release if available

#### "Request must be approved"
- **Cause**: Trying to release a request that isn't approved
- **Solution**: Ensure request is properly approved before release

### Getting Help

- **System Administrator**: Contact your system administrator for technical issues
- **GSO Supervisor**: Contact your supervisor for policy or procedure questions
- **User Manual**: Refer to this guide for step-by-step instructions
- **Training**: Request additional training if needed

## Best Practices

### Efficient Workflow

1. **Use Filters**: Start with filters to narrow down requests
2. **Batch Operations**: Group similar requests for batch processing
3. **Regular Monitoring**: Check the dashboard regularly for new requests
4. **Stock Awareness**: Monitor stock levels to anticipate issues

### Quality Control

1. **Verify Details**: Always review request details before releasing
2. **Check Quantities**: Ensure requested quantities are reasonable
3. **Document Issues**: Use release notes to document any issues
4. **Follow Up**: Monitor partial releases for completion

### System Maintenance

1. **Regular Refresh**: Use manual refresh during busy periods
2. **Clear Selections**: Clear batch selections when switching tasks
3. **Log Out Properly**: Always log out when finished
4. **Report Issues**: Report any system issues promptly

This user guide provides comprehensive instructions for using the Enhanced Release Management System effectively. Regular practice with these features will improve efficiency and accuracy in supply release operations.
