<!-- Release List -->
<div class="space-y-4">
    {% if page_obj %}

    <!-- Bulk Selection Header (Desktop) -->
    <div class="hidden md:block bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <input type="checkbox"
                       @change="toggleAll()"
                       :checked="selectedRequests.length === {{ page_obj|length }}"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <span class="text-sm font-medium text-gray-700">
                    Select All (<span x-text="selectedRequests.length"></span> selected)
                </span>
            </div>
            <div class="text-sm text-gray-500">
                {{ page_obj.paginator.count }} total requests ready for release
            </div>
        </div>
    </div>

    <!-- Mobile View Toggle -->
    <div class="md:hidden bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Release Requests</h3>
            <span class="text-sm text-gray-500">{{ page_obj.paginator.count }} items</span>
        </div>
    </div>

    <!-- Request Cards Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {% for request in page_obj %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200" id="request-{{ request.id }}">
            <!-- Card Header -->
            <div class="p-6 border-b border-gray-100">
                <div class="flex items-start justify-between">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               name="request_checkbox"
                               value="{{ request.id }}"
                               @change="toggleRequest({{ request.id }})"
                               :checked="selectedRequests.includes({{ request.id }})"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">
                                <a href="{% if request.is_batch_request %}{% url 'supply:batch_request_detail' request.id %}{% else %}{% url 'supply:gso_request_detail' request.id %}{% endif %}"
                                   class="text-blue-600 hover:text-blue-800 transition-colors">
                                    {{ request.request_id }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-600">
                                {% if request.is_batch_request %}
                                    Batch Request ({{ request.request_items.count }} items)
                                {% else %}
                                    {{ request.item.name }}
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <!-- Status Badge -->
                    {% if request.can_be_released %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Ready to Release
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Insufficient Stock
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Department & Requester -->
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Department</dt>
                            <dd class="mt-1 text-sm text-gray-900 font-medium">{{ request.department }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Requester</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ request.requester.get_full_name|default:request.requester.username }}
                            </dd>
                        </div>
                    </div>

                    <!-- Quantity & Stock -->
                    <div class="space-y-3">
                        {% if request.is_batch_request %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Items</dt>
                                <dd class="mt-1 text-lg font-semibold text-gray-900">
                                    {{ request.request_items.count }} items
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Stock Status</dt>
                                <dd class="mt-1 text-sm font-semibold {% if request.can_be_released %}text-green-600{% else %}text-red-600{% endif %}">
                                    {{ request.get_stock_status.message }}
                                </dd>
                            </div>
                        {% else %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Requested Quantity</dt>
                                <dd class="mt-1 text-lg font-semibold text-gray-900">
                                    {{ request.quantity }} {{ request.item.unit }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Available Stock</dt>
                                <dd class="mt-1 text-lg font-semibold {% if request.can_be_released %}text-green-600{% else %}text-red-600{% endif %}">
                                    {{ request.item.current_stock }} {{ request.item.unit }}
                                </dd>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Approval Info -->
                    <div class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Approved Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ request.approved_at|date:"M d, Y" }}</dd>
                            <dd class="text-xs text-gray-500">{{ request.approved_at|time:"g:i A" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Purpose</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ request.purpose|truncatechars:60 }}</dd>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar for Stock Availability -->
                {% if not request.is_batch_request and request.item.current_stock > 0 %}
                <div class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Stock Availability</span>
                        <span class="font-medium {% if request.can_be_released %}text-green-600{% else %}text-red-600{% endif %}">
                            {% widthratio request.quantity request.item.current_stock 100 %}% of available stock
                        </span>
                    </div>
                    <div class="mt-2 bg-gray-200 rounded-full h-2">
                        <div class="{% if request.can_be_released %}bg-green-500{% else %}bg-red-500{% endif %} h-2 rounded-full"
                             style="width: {% if request.can_be_released %}100{% else %}{% widthratio request.item.current_stock request.quantity 100 %}{% endif %}%"></div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Card Footer -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                <div class="flex items-center justify-between">
                    <div class="flex space-x-3">
                        <a href="{% if request.is_batch_request %}{% url 'supply:batch_request_detail' request.id %}{% else %}{% url 'supply:gso_request_detail' request.id %}{% endif %}"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Details
                        </a>
                    </div>

                    <div>
                        {% if request.can_be_released %}
                            <form hx-post="{% url 'supply:release_request' request.id %}"
                                  hx-target="#modal-container"
                                  hx-swap="innerHTML"
                                  hx-confirm="Are you sure you want to release this request?"
                                  style="display: inline;">
                                {% csrf_token %}
                                <button type="submit"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    Release Now
                                </button>
                            </form>
                        {% else %}
                            <button disabled
                                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                </svg>
                                Cannot Release
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Mobile Pagination -->
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        Next
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                {% endif %}
            </div>

            <!-- Desktop Pagination -->
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium text-gray-900">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium text-gray-900">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium text-gray-900">{{ page_obj.paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               class="relative inline-flex items-center px-3 py-2 rounded-l-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               class="relative inline-flex items-center px-3 py-2 rounded-r-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% else %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No approved requests</h3>
        <p class="mt-1 text-sm text-gray-500">There are no approved requests ready for release.</p>
    </div>
    {% endif %}
</div>
