<!-- Release List -->
<div class="space-y-4">
    {% if page_obj %}

    <!-- Enhanced Bulk Selection Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div class="flex items-center space-x-3">
                <input type="checkbox"
                       @change="toggleAll()"
                       :checked="selectedRequests.length === {{ page_obj|length }} && {{ page_obj|length }} > 0"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <span class="text-sm font-medium text-gray-700">
                    Select All (<span x-text="selectedRequests.length"></span> selected)
                </span>
            </div>
            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                <div class="text-sm text-gray-500">
                    {{ page_obj.paginator.count }} total requests
                </div>
                <div class="flex items-center space-x-4 text-xs">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
                        <span class="text-gray-600">Available ({{ available_count }})</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-1"></div>
                        <span class="text-gray-600">Partial ({{ partial_count }})</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
                        <span class="text-gray-600">Insufficient ({{ insufficient_count }})</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Request Cards Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {% for request in page_obj %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200" id="request-{{ request.id }}">
            <!-- Card Header -->
            <div class="p-6 border-b border-gray-100">
                <div class="flex items-start justify-between">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox"
                               name="request_checkbox"
                               value="{{ request.id }}"
                               @change="toggleRequest({{ request.id }})"
                               :checked="selectedRequests.includes({{ request.id }})"
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">
                                <a href="{% if request.is_batch_request %}{% url 'supply:batch_request_detail' request.id %}{% else %}{% url 'supply:gso_request_detail' request.id %}{% endif %}"
                                   class="text-blue-600 hover:text-blue-800 transition-colors">
                                    {{ request.request_id }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-600">
                                {% if request.is_batch_request %}
                                    Batch Request ({{ request.request_items.count }} items)
                                {% else %}
                                    {{ request.item.name }}
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <!-- Enhanced Status Badge -->
                    {% if request.can_be_released %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            Ready to Release
                        </span>
                    {% elif request.is_batch_request and request.can_partial_release %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Partial Available
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            Insufficient Stock
                        </span>
                    {% endif %}
                </div>
            </div>

            <!-- Enhanced Card Body -->
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Department & Requester -->
                    <div class="space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                Department
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900 font-medium">{{ request.department }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Requester
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ request.requester.get_full_name|default:request.requester.username }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                Approved
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ request.approved_at|date:"M d, Y" }}</dd>
                            <dd class="text-xs text-gray-500">{{ request.approved_at|time:"g:i A" }}</dd>
                        </div>
                    </div>

                    <!-- Enhanced Stock Information -->
                    <div class="space-y-4">
                        {% if request.is_batch_request %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                    Batch Items
                                </dt>
                                <dd class="mt-1 text-lg font-semibold text-gray-900">
                                    {{ request.request_items.count }} items
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    Stock Status
                                </dt>
                                <dd class="mt-1 text-sm font-semibold {% if request.can_be_released %}text-green-600{% elif request.can_partial_release %}text-yellow-600{% else %}text-red-600{% endif %}">
                                    {{ request.get_stock_status.message }}
                                </dd>
                                {% with stock_details=request.get_stock_status.details %}
                                    {% if stock_details.available_items != stock_details.total_items %}
                                        <dd class="text-xs text-gray-500 mt-1">
                                            {{ stock_details.available_items }}/{{ stock_details.total_items }} items available
                                        </dd>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        {% else %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                                    </svg>
                                    Requested
                                </dt>
                                <dd class="mt-1 text-lg font-semibold text-gray-900">
                                    {{ request.quantity }} {{ request.item.unit }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    Available
                                </dt>
                                <dd class="mt-1 text-lg font-semibold {% if request.can_be_released %}text-green-600{% else %}text-red-600{% endif %}">
                                    {{ request.item.current_stock }} {{ request.item.unit }}
                                </dd>
                                {% if not request.can_be_released %}
                                    {% with stock_details=request.get_stock_status.details %}
                                        <dd class="text-xs text-red-500 mt-1">
                                            Short by {{ stock_details.shortage }} {{ request.item.unit }}
                                        </dd>
                                    {% endwith %}
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Purpose & Additional Info -->
                    <div class="space-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Purpose
                            </dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ request.purpose|truncatechars:80 }}</dd>
                        </div>
                        {% if request.approval_remarks %}
                            <div>
                                <dt class="text-sm font-medium text-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                                    </svg>
                                    Approval Notes
                                </dt>
                                <dd class="mt-1 text-sm text-gray-600 italic">{{ request.approval_remarks|truncatechars:60 }}</dd>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Progress Bar for Stock Availability -->
                {% if not request.is_batch_request and request.item.current_stock > 0 %}
                <div class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-500">Stock Availability</span>
                        <span class="font-medium {% if request.can_be_released %}text-green-600{% else %}text-red-600{% endif %}">
                            {% widthratio request.quantity request.item.current_stock 100 %}% of available stock
                        </span>
                    </div>
                    <div class="mt-2 bg-gray-200 rounded-full h-2">
                        <div class="{% if request.can_be_released %}bg-green-500{% else %}bg-red-500{% endif %} h-2 rounded-full"
                             style="width: {% if request.can_be_released %}100{% else %}{% widthratio request.item.current_stock request.quantity 100 %}{% endif %}%"></div>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Enhanced Card Footer -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 rounded-b-xl">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                    <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-2 sm:space-y-0">
                        <a href="{% if request.is_batch_request %}{% url 'supply:batch_request_detail' request.id %}{% else %}{% url 'supply:gso_request_detail' request.id %}{% endif %}"
                           class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View Details
                        </a>
                        {% if request.is_batch_request and request.get_stock_status.details.item_details %}
                            <button type="button"
                                    @click="showStockDetails{{ request.id }} = !showStockDetails{{ request.id }}"
                                    x-data="{ showStockDetails{{ request.id }}: false }"
                                    class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Stock Details
                            </button>
                        {% endif %}
                    </div>

                    <div class="flex flex-col sm:flex-row sm:space-x-3 space-y-2 sm:space-y-0">
                        {% if request.can_be_released %}
                            <form hx-post="{% url 'supply:release_request' request.id %}"
                                  hx-target="#modal-container"
                                  hx-swap="innerHTML"
                                  hx-confirm="Are you sure you want to release this request?"
                                  style="display: inline;">
                                {% csrf_token %}
                                <button type="submit"
                                        class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    Release Now
                                </button>
                            </form>
                        {% elif request.is_batch_request and request.can_partial_release %}
                            <form hx-post="{% url 'supply:release_request' request.id %}"
                                  hx-target="#modal-container"
                                  hx-swap="innerHTML"
                                  hx-confirm="This will partially release available items. Continue?"
                                  style="display: inline;">
                                {% csrf_token %}
                                <input type="hidden" name="allow_partial" value="true">
                                <button type="submit"
                                        class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    Partial Release
                                </button>
                            </form>
                        {% else %}
                            <button disabled
                                    class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-100 cursor-not-allowed">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                </svg>
                                Cannot Release
                            </button>
                        {% endif %}
                    </div>
                </div>

                <!-- Stock Details Expandable Section for Batch Requests -->
                {% if request.is_batch_request and request.get_stock_status.details.item_details %}
                    <div x-show="showStockDetails{{ request.id }}"
                         x-collapse
                         x-data="{ showStockDetails{{ request.id }}: false }"
                         class="mt-4 pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-900 mb-3">Item Stock Details</h4>
                        <div class="space-y-2">
                            {% for item_detail in request.get_stock_status.details.item_details %}
                                <div class="flex items-center justify-between text-sm">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 rounded-full {% if item_detail.can_fulfill %}bg-green-500{% else %}bg-red-500{% endif %}"></div>
                                        <span class="font-medium">{{ item_detail.item_name }}</span>
                                    </div>
                                    <div class="text-right">
                                        <div class="{% if item_detail.can_fulfill %}text-green-600{% else %}text-red-600{% endif %}">
                                            {{ item_detail.available }}/{{ item_detail.requested }} {{ item_detail.unit }}
                                        </div>
                                        {% if not item_detail.can_fulfill %}
                                            <div class="text-xs text-red-500">
                                                Short: {{ item_detail.shortage }} {{ item_detail.unit }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Mobile Pagination -->
            <div class="flex-1 flex justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Previous
                    </a>
                {% endif %}
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        Next
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                {% endif %}
            </div>

            <!-- Desktop Pagination -->
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium text-gray-900">{{ page_obj.start_index }}</span>
                        to
                        <span class="font-medium text-gray-900">{{ page_obj.end_index }}</span>
                        of
                        <span class="font-medium text-gray-900">{{ page_obj.paginator.count }}</span>
                        results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               class="relative inline-flex items-center px-3 py-2 rounded-l-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}&department={{ department_filter }}&search={{ search_query }}&sort={{ sort_by }}"
                               class="relative inline-flex items-center px-3 py-2 rounded-r-lg border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% else %}
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No approved requests</h3>
        <p class="mt-1 text-sm text-gray-500">There are no approved requests ready for release.</p>
    </div>
    {% endif %}
</div>
