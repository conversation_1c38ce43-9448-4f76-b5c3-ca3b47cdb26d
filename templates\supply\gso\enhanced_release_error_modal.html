<!-- Enhanced Release Error Modal -->
<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-10 mx-auto p-6 border max-w-lg shadow-lg rounded-xl bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <div class="text-center">
            <!-- Error Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            
            <!-- Error Message -->
            <h3 class="text-xl leading-6 font-semibold text-gray-900 mb-4">
                Release Failed
            </h3>
            <div class="mb-6">
                <p class="text-sm text-gray-600 mb-4">
                    {{ error_message }}
                </p>
                
                {% if error_details %}
                    <!-- Error Details -->
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-left">
                        <h4 class="text-sm font-medium text-red-900 mb-3">Error Details</h4>
                        
                        {% if error_details.failed_item %}
                            <div class="space-y-2">
                                <div class="text-xs text-red-700 font-medium">Failed Item:</div>
                                <div class="flex justify-between text-xs text-red-800">
                                    <span>{{ error_details.failed_item }}</span>
                                    <span>{{ error_details.available }}/{{ error_details.requested }} available</span>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if error_details.error %}
                            <div class="mt-3 pt-3 border-t border-red-300">
                                <div class="text-xs text-red-700 font-medium mb-2">Error:</div>
                                <div class="text-xs text-red-800">{{ error_details.error }}</div>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
                
                {% if stock_status %}
                    <!-- Stock Status Details -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-left mt-4">
                        <h4 class="text-sm font-medium text-yellow-900 mb-3">Stock Status</h4>
                        
                        <div class="text-xs text-yellow-800 mb-2">{{ stock_status.message }}</div>
                        
                        {% if stock_status.details.item_details %}
                            <div class="space-y-2">
                                {% for item_detail in stock_status.details.item_details %}
                                    <div class="flex justify-between text-xs">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 rounded-full {% if item_detail.can_fulfill %}bg-green-500{% else %}bg-red-500{% endif %}"></div>
                                            <span class="{% if item_detail.can_fulfill %}text-green-800{% else %}text-red-800{% endif %}">{{ item_detail.item_name }}</span>
                                        </div>
                                        <div class="{% if item_detail.can_fulfill %}text-green-800{% else %}text-red-800{% endif %}">
                                            {{ item_detail.available }}/{{ item_detail.requested }} {{ item_detail.unit }}
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% elif stock_status.details.requested %}
                            <div class="flex justify-between text-xs text-yellow-800">
                                <span>Requested:</span>
                                <span>{{ stock_status.details.requested }} {{ stock_status.details.unit }}</span>
                            </div>
                            <div class="flex justify-between text-xs text-yellow-800">
                                <span>Available:</span>
                                <span>{{ stock_status.details.available }} {{ stock_status.details.unit }}</span>
                            </div>
                            {% if stock_status.details.shortage %}
                                <div class="flex justify-between text-xs text-red-800 font-medium">
                                    <span>Shortage:</span>
                                    <span>{{ stock_status.details.shortage }} {{ stock_status.details.unit }}</span>
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
            <button type="button"
                    @click="show = false; document.getElementById('modal-container').innerHTML = '';"
                    class="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 border border-transparent rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-white hover:from-gray-700 hover:to-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                Close
            </button>
            {% if supply_request %}
                <a href="{% if supply_request.is_batch_request %}{% url 'supply:batch_request_detail' supply_request.id %}{% else %}{% url 'supply:gso_request_detail' supply_request.id %}{% endif %}"
                   class="bg-blue-100 border border-blue-300 rounded-lg shadow-sm py-3 px-4 inline-flex justify-center items-center text-sm font-medium text-blue-700 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View Request
                </a>
            {% endif %}
        </div>
    </div>
</div>
