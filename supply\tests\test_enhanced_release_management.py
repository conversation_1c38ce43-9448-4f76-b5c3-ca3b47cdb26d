"""
Comprehensive tests for enhanced release management functionality
"""
import json
from datetime import datetime, timedelta
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch, MagicMock

from supply.models import (
    SupplyRequest, SupplyItem, RequestItem, InventoryTransaction, 
    AuditLog, UserProfile
)


class EnhancedReleaseManagementTestCase(TestCase):
    """Test case for enhanced release management functionality"""
    
    def setUp(self):
        """Set up test data"""
        # Create test users
        self.gso_user = User.objects.create_user(
            username='gso_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='GSO',
            last_name='User'
        )
        
        self.dept_user = User.objects.create_user(
            username='dept_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='Department',
            last_name='User'
        )
        
        # Create user profiles
        UserProfile.objects.create(
            user=self.gso_user,
            role='GSO',
            department='General Services Office'
        )
        
        UserProfile.objects.create(
            user=self.dept_user,
            role='DEPARTMENT_USER',
            department='IT Department'
        )
        
        # Create test items
        self.item1 = SupplyItem.objects.create(
            name='Test Item 1',
            description='Test Description 1',
            unit='pcs',
            current_stock=100,
            minimum_stock=10,
            maximum_stock=200
        )
        
        self.item2 = SupplyItem.objects.create(
            name='Test Item 2',
            description='Test Description 2',
            unit='pcs',
            current_stock=5,  # Low stock for testing
            minimum_stock=10,
            maximum_stock=200
        )
        
        # Create test requests
        self.single_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item1,
            quantity=10,
            purpose='Testing single request',
            department='IT Department',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=datetime.now()
        )
        
        self.batch_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            purpose='Testing batch request',
            department='IT Department',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=datetime.now(),
            is_batch_request=True
        )
        
        # Create batch request items
        RequestItem.objects.create(
            request=self.batch_request,
            item=self.item1,
            quantity=20,
            approved_quantity=20
        )
        
        RequestItem.objects.create(
            request=self.batch_request,
            item=self.item2,
            quantity=10,  # More than available stock
            approved_quantity=10
        )
        
        self.client = Client()
    
    def test_release_management_view_access(self):
        """Test access control for release management view"""
        # Test unauthenticated access
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test department user access (should be denied)
        self.client.login(username='dept_user', password='testpass123')
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 403)  # Forbidden
        
        # Test GSO user access (should be allowed)
        self.client.login(username='gso_user', password='testpass123')
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 200)
    
    def test_release_management_filtering(self):
        """Test enhanced filtering functionality"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test department filter
        response = self.client.get(reverse('supply:release_management'), {
            'department': 'IT Department'
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'IT Department')
        
        # Test stock filter - available
        response = self.client.get(reverse('supply:release_management'), {
            'stock_filter': 'available'
        })
        self.assertEqual(response.status_code, 200)
        
        # Test stock filter - insufficient
        response = self.client.get(reverse('supply:release_management'), {
            'stock_filter': 'insufficient'
        })
        self.assertEqual(response.status_code, 200)
        
        # Test request type filter
        response = self.client.get(reverse('supply:release_management'), {
            'request_type': 'single'
        })
        self.assertEqual(response.status_code, 200)
        
        response = self.client.get(reverse('supply:release_management'), {
            'request_type': 'batch'
        })
        self.assertEqual(response.status_code, 200)
    
    def test_enhanced_stock_status_method(self):
        """Test enhanced get_stock_status method"""
        # Test single request with sufficient stock
        stock_status = self.single_request.get_stock_status()
        self.assertTrue(stock_status['available'])
        self.assertIn('details', stock_status)
        self.assertEqual(stock_status['details']['requested'], 10)
        self.assertEqual(stock_status['details']['available'], 100)
        
        # Test batch request with mixed stock availability
        stock_status = self.batch_request.get_stock_status()
        self.assertFalse(stock_status['available'])  # Not all items available
        self.assertIn('details', stock_status)
        self.assertEqual(stock_status['details']['total_items'], 2)
        self.assertEqual(stock_status['details']['available_items'], 1)  # Only item1 available
    
    def test_release_progress_tracking(self):
        """Test release progress tracking functionality"""
        # Test unreleased request
        progress = self.single_request.get_release_progress()
        self.assertFalse(progress['is_released'])
        self.assertEqual(progress['progress_percentage'], 0)
        
        # Release the request
        result = self.single_request.release_with_tracking(
            released_by=self.gso_user,
            remarks='Test release'
        )
        self.assertTrue(result['success'])
        
        # Test released request progress
        progress = self.single_request.get_release_progress()
        self.assertTrue(progress['is_released'])
        self.assertEqual(progress['progress_percentage'], 100)
    
    def test_partial_release_functionality(self):
        """Test partial release functionality for batch requests"""
        # Test can_partial_release method
        self.assertTrue(self.batch_request.can_partial_release())
        
        # Test partial release
        result = self.batch_request.release_with_tracking(
            released_by=self.gso_user,
            remarks='Test partial release',
            partial_release=True
        )
        
        self.assertTrue(result['success'])
        self.assertTrue(result['details']['partial_release'])
        self.assertEqual(len(result['details']['released_items']), 1)  # Only item1 released
        self.assertEqual(len(result['details']['failed_items']), 1)    # item2 failed
    
    def test_enhanced_release_request_view(self):
        """Test enhanced release request view with detailed tracking"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test successful release
        response = self.client.post(
            reverse('supply:release_request', args=[self.single_request.id]),
            {'release_remarks': 'Test release'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        
        # Verify request was released
        self.single_request.refresh_from_db()
        self.assertEqual(self.single_request.status, 'RELEASED')
        
        # Verify inventory was updated
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.current_stock, 90)  # 100 - 10
    
    def test_batch_operations_with_partial_release(self):
        """Test batch operations with partial release support"""
        self.client.login(username='gso_user', password='testpass123')
        
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_release',
                'selected_requests': [self.batch_request.id],
                'release_notes': 'Test batch release',
                'allow_partial': 'true'
            },
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        
        # Verify partial release occurred
        self.batch_request.refresh_from_db()
        self.assertEqual(self.batch_request.status, 'RELEASED')
    
    def test_error_handling_invalid_request_id(self):
        """Test error handling for invalid request IDs"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test with invalid request ID
        response = self.client.post(
            reverse('supply:release_request', args=['invalid']),
            {'release_remarks': 'Test release'}
        )
        self.assertEqual(response.status_code, 302)  # Redirect
        
        # Check error message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Invalid request ID' in str(m) for m in messages))
    
    def test_error_handling_insufficient_stock(self):
        """Test error handling for insufficient stock scenarios"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Create request with insufficient stock
        insufficient_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=self.item2,  # Only 5 in stock
            quantity=20,      # Requesting 20
            purpose='Testing insufficient stock',
            department='IT Department',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=datetime.now()
        )
        
        response = self.client.post(
            reverse('supply:release_request', args=[insufficient_request.id]),
            {'release_remarks': 'Test release'},
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Cannot release supplies')
    
    def test_audit_logging(self):
        """Test audit logging for release operations"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Count initial audit logs
        initial_count = AuditLog.objects.count()
        
        # Perform release
        self.client.post(
            reverse('supply:release_request', args=[self.single_request.id]),
            {'release_remarks': 'Test release'}
        )
        
        # Verify audit log was created
        self.assertEqual(AuditLog.objects.count(), initial_count + 1)
        
        # Verify audit log details
        audit_log = AuditLog.objects.latest('created_at')
        self.assertEqual(audit_log.user, self.gso_user)
        self.assertEqual(audit_log.action, 'RELEASE')
        self.assertEqual(audit_log.model_name, 'SupplyRequest')
    
    def test_inventory_transaction_creation(self):
        """Test inventory transaction creation during release"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Count initial transactions
        initial_count = InventoryTransaction.objects.count()
        
        # Perform release
        self.client.post(
            reverse('supply:release_request', args=[self.single_request.id]),
            {'release_remarks': 'Test release'}
        )
        
        # Verify transaction was created
        self.assertEqual(InventoryTransaction.objects.count(), initial_count + 1)
        
        # Verify transaction details
        transaction = InventoryTransaction.objects.latest('created_at')
        self.assertEqual(transaction.item, self.item1)
        self.assertEqual(transaction.transaction_type, 'OUT')
        self.assertEqual(transaction.quantity, 10)
        self.assertEqual(transaction.performed_by, self.gso_user)
    
    def test_htmx_responses(self):
        """Test HTMX-specific responses"""
        self.client.login(username='gso_user', password='testpass123')
        
        # Test HTMX request to release management
        response = self.client.get(
            reverse('supply:release_management'),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        # Should return partial template
        self.assertTemplateUsed(response, 'supply/gso/release_list.html')
        
        # Test non-HTMX request
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 200)
        # Should return full template
        self.assertTemplateUsed(response, 'supply/gso/release_management.html')


class ReleaseManagementIntegrationTestCase(TestCase):
    """Integration tests for release management workflow"""

    def setUp(self):
        """Set up integration test data"""
        # Create test users
        self.gso_user = User.objects.create_user(
            username='gso_integration',
            email='<EMAIL>',
            password='testpass123'
        )

        self.dept_user = User.objects.create_user(
            username='dept_integration',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create user profiles
        UserProfile.objects.create(
            user=self.gso_user,
            role='GSO',
            department='General Services Office'
        )

        UserProfile.objects.create(
            user=self.dept_user,
            role='DEPARTMENT_USER',
            department='Finance Department'
        )

        # Create multiple test items
        self.items = []
        for i in range(5):
            item = SupplyItem.objects.create(
                name=f'Integration Item {i+1}',
                description=f'Integration test item {i+1}',
                unit='pcs',
                current_stock=50 + (i * 10),
                minimum_stock=10,
                maximum_stock=200
            )
            self.items.append(item)

        self.client = Client()

    def test_complete_release_workflow(self):
        """Test complete release workflow from request to completion"""
        self.client.login(username='gso_user', password='testpass123')

        # Create multiple requests
        requests = []
        for i, item in enumerate(self.items[:3]):
            request = SupplyRequest.objects.create(
                requester=self.dept_user,
                item=item,
                quantity=10,
                purpose=f'Integration test request {i+1}',
                department='Finance Department',
                status='APPROVED',
                approved_by=self.gso_user,
                approved_at=datetime.now()
            )
            requests.append(request)

        # Test release management page loads with requests
        response = self.client.get(reverse('supply:release_management'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Finance Department')

        # Test batch release
        request_ids = [req.id for req in requests]
        response = self.client.post(
            reverse('supply:batch_operations'),
            {
                'action': 'bulk_release',
                'selected_requests': request_ids,
                'release_notes': 'Integration test batch release'
            }
        )

        # Verify all requests were released
        for request in requests:
            request.refresh_from_db()
            self.assertEqual(request.status, 'RELEASED')

        # Verify inventory was updated
        for i, item in enumerate(self.items[:3]):
            item.refresh_from_db()
            expected_stock = (50 + (i * 10)) - 10  # Original stock - released quantity
            self.assertEqual(item.current_stock, expected_stock)

    def test_mixed_stock_availability_scenario(self):
        """Test scenario with mixed stock availability"""
        self.client.login(username='gso_user', password='testpass123')

        # Create batch request with mixed availability
        batch_request = SupplyRequest.objects.create(
            requester=self.dept_user,
            purpose='Mixed availability test',
            department='Finance Department',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=datetime.now(),
            is_batch_request=True
        )

        # Add items with different availability
        RequestItem.objects.create(
            request=batch_request,
            item=self.items[0],  # Has 50 stock
            quantity=30,         # Available
            approved_quantity=30
        )

        RequestItem.objects.create(
            request=batch_request,
            item=self.items[1],  # Has 60 stock
            quantity=80,         # Not available
            approved_quantity=80
        )

        # Test stock status
        stock_status = batch_request.get_stock_status()
        self.assertFalse(stock_status['available'])
        self.assertEqual(stock_status['details']['available_items'], 1)
        self.assertEqual(stock_status['details']['total_items'], 2)

        # Test partial release
        result = batch_request.release_with_tracking(
            released_by=self.gso_user,
            remarks='Partial release test',
            partial_release=True
        )

        self.assertTrue(result['success'])
        self.assertTrue(result['details']['partial_release'])
        self.assertEqual(len(result['details']['released_items']), 1)
        self.assertEqual(len(result['details']['failed_items']), 1)

    def test_concurrent_release_handling(self):
        """Test handling of concurrent release attempts"""
        self.client.login(username='gso_user', password='testpass123')

        # Create request with limited stock
        limited_item = SupplyItem.objects.create(
            name='Limited Stock Item',
            description='Item with limited stock',
            unit='pcs',
            current_stock=10,
            minimum_stock=5,
            maximum_stock=50
        )

        request1 = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=limited_item,
            quantity=8,
            purpose='First request',
            department='Finance Department',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=datetime.now()
        )

        request2 = SupplyRequest.objects.create(
            requester=self.dept_user,
            item=limited_item,
            quantity=5,
            purpose='Second request',
            department='Finance Department',
            status='APPROVED',
            approved_by=self.gso_user,
            approved_at=datetime.now()
        )

        # Release first request
        result1 = request1.release_with_tracking(
            released_by=self.gso_user,
            remarks='First release'
        )
        self.assertTrue(result1['success'])

        # Try to release second request (should fail due to insufficient stock)
        result2 = request2.release_with_tracking(
            released_by=self.gso_user,
            remarks='Second release'
        )
        self.assertFalse(result2['success'])
        self.assertIn('Insufficient stock', result2['message'])

    def test_performance_with_large_dataset(self):
        """Test performance with larger dataset"""
        self.client.login(username='gso_user', password='testpass123')

        # Create many requests
        requests = []
        for i in range(20):
            request = SupplyRequest.objects.create(
                requester=self.dept_user,
                item=self.items[i % len(self.items)],
                quantity=5,
                purpose=f'Performance test request {i+1}',
                department='Finance Department',
                status='APPROVED',
                approved_by=self.gso_user,
                approved_at=datetime.now() - timedelta(days=i)
            )
            requests.append(request)

        # Test release management page performance
        import time
        start_time = time.time()

        response = self.client.get(reverse('supply:release_management'))

        end_time = time.time()
        response_time = end_time - start_time

        self.assertEqual(response.status_code, 200)
        self.assertLess(response_time, 2.0)  # Should load within 2 seconds

        # Test filtering performance
        start_time = time.time()

        response = self.client.get(reverse('supply:release_management'), {
            'department': 'Finance Department',
            'stock_filter': 'available',
            'search': 'Performance'
        })

        end_time = time.time()
        filter_time = end_time - start_time

        self.assertEqual(response.status_code, 200)
        self.assertLess(filter_time, 1.0)  # Filtering should be fast
